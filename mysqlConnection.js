// mysqlConnection.js

const mysql = require('mysql');
const express = require('express');
const app = express();

const connection = mysql.createConnection({
  host: 'srv788.hstgr.io',
  user: 'u761302346_flutter_user',
  password: '<PERSON><PERSON><PERSON>@91212',
  database: 'u761302346_flutter_auth'
});

connection.connect(err => {
  if (err) {
    console.error('Error connecting to DB:', err);
    return;
  }
  console.log('Connected to Hostinger MySQL!');
});

app.get('/users', (req, res) => {
  connection.query('SELECT * FROM users', (err, results) => {
    if (err) {
      console.error('Error in query:', err);
      return res.status(500).send('Database error');
    }
    res.json(results);
  });
});

app.listen(3000, () => {
  console.log('Server running at http://localhost:3000');
});
