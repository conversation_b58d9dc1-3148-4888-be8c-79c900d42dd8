# Notion Knowledge Vault - Quick Reference Card

## 🚀 Getting Started (First 5 Minutes)

### Essential Bookmarks
- **Main Dashboard:** [Knowledge Vault Home]
- **My Tasks:** [Tasks & Projects → My Tasks View]
- **Team Calendar:** [Meeting Notes → Calendar View]
- **My Department:** [Your Department Teamspace]

### Daily Actions Checklist
- [ ] Check dashboard for announcements
- [ ] Review "My Tasks" for today's priorities
- [ ] Update task progress and status
- [ ] Check for meeting notes requiring action

## 📝 Creating Content

### Quick Page Creation
1. **Press `/` for command menu**
2. **Choose template or page type**
3. **Add to appropriate database**
4. **Fill required properties**
5. **Save and share**

### Essential Templates
- **Meeting Notes:** Use for all team meetings
- **Project Documentation:** Use for new projects
- **Process Documentation:** Use for workflows
- **Task Creation:** Use for action items

### Property Quick Guide
| Property Type | When to Use | Example |
|---------------|-------------|---------|
| Select | Single choice | Status, Priority, Type |
| Multi-select | Multiple choices | Tags, Departments |
| Person | Assign ownership | Owner, Assignee |
| Date | Deadlines/schedules | Due Date, Meeting Date |
| Relation | Link databases | Projects ↔ Tasks |

## 🔍 Finding Information

### Search Tips
- **Use specific keywords** from titles and content
- **Filter by properties** (status, department, date)
- **Use @ mentions** to find people-related content
- **Search within databases** for targeted results

### Navigation Shortcuts
- **Ctrl/Cmd + P:** Quick find and open pages
- **Ctrl/Cmd + Shift + L:** Toggle dark/light mode
- **Ctrl/Cmd + /:** Show all keyboard shortcuts
- **Ctrl/Cmd + Shift + N:** Create new page

### Database Views
- **Table View:** Spreadsheet-like data view
- **Board View:** Kanban-style project management
- **Calendar View:** Time-based scheduling
- **Gallery View:** Visual card layout

## 👥 Collaboration

### Commenting and Feedback
1. **Highlight text** to add inline comments
2. **Use @ mentions** to notify specific people
3. **Resolve comments** when addressed
4. **Use emoji reactions** for quick feedback

### Sharing and Permissions
- **Share button:** Control who can access content
- **Comment permissions:** Allow feedback without editing
- **Edit permissions:** Full collaboration access
- **Public sharing:** External stakeholder access

### Meeting Notes Workflow
1. **Create from template** before meeting
2. **Share agenda** 24 hours in advance
3. **Take notes** during meeting
4. **Add action items** with owners and dates
5. **Share final notes** within 2 hours
6. **Follow up** on action items

## 🎯 Task Management

### Task Status Workflow
```
Not Started → In Progress → In Review → Completed
                    ↓
                 On Hold (if needed)
```

### Priority Levels
- **Critical:** Urgent, blocking other work
- **High:** Important, time-sensitive
- **Medium:** Standard priority
- **Low:** Nice to have, flexible timing

### Task Properties Checklist
- [ ] Clear, descriptive title
- [ ] Assigned to specific person
- [ ] Due date set
- [ ] Priority level assigned
- [ ] Linked to relevant project/documentation
- [ ] Progress percentage updated

## 📊 Database Quick Actions

### Meeting Notes Database
- **Create new meeting:** Use template
- **Find past meetings:** Filter by date/attendees
- **Track action items:** Use "Action Items Pending" view
- **Department meetings:** Filter by department

### Documentation Hub
- **Add new doc:** Use appropriate template
- **Find documentation:** Search by type or department
- **Review cycle:** Check "Needs Review" view
- **Update status:** Draft → Review → Published

### Tasks & Projects
- **Add new task:** Assign owner and due date
- **Check my work:** Use "My Tasks" view
- **Project overview:** Filter by project name
- **Team workload:** Use "Department Workload" view

## 🔧 Troubleshooting

### Common Issues and Solutions

#### Can't Find a Page
- **Check spelling** in search terms
- **Use filters** to narrow results
- **Check if page is archived**
- **Ask page owner** for access

#### Permission Denied
- **Request access** from page owner
- **Check if you're in correct teamspace**
- **Contact admin** for workspace access
- **Verify your account** is added to workspace

#### Template Not Working
- **Check if template is published**
- **Verify you have edit permissions**
- **Try duplicating** instead of using template
- **Contact admin** for template issues

#### Mobile App Issues
- **Check internet connection**
- **Update app** to latest version
- **Clear app cache** if needed
- **Use web browser** as backup

## 📱 Mobile Quick Tips

### Essential Mobile Actions
- **Swipe left** on tasks to change status
- **Pull down** to refresh content
- **Use voice notes** for quick capture
- **Offline mode** for viewing content

### Mobile Shortcuts
- **Quick Add:** Plus button for new content
- **Search:** Magnifying glass icon
- **Recent:** Clock icon for recent pages
- **Favorites:** Star icon for bookmarked pages

## 🎨 Formatting Quick Guide

### Text Formatting
- **Bold:** `**text**` or Ctrl/Cmd + B
- **Italic:** `*text*` or Ctrl/Cmd + I
- **Code:** `` `text` `` or Ctrl/Cmd + E
- **Link:** `[text](url)` or Ctrl/Cmd + K

### Block Types (Type `/` + keyword)
- `/h1, /h2, /h3` - Headers
- `/bullet` - Bullet list
- `/number` - Numbered list
- `/todo` - Checkbox list
- `/quote` - Quote block
- `/code` - Code block
- `/table` - Table
- `/callout` - Callout box

### Useful Symbols and Emojis
- **Status:** ✅ ❌ ⏳ 🔄 ⚠️
- **Priority:** 🔴 🟡 🟢 🔵
- **Types:** 📝 📊 🎯 💡 🔧
- **Departments:** 💻 🎨 📢 💰 👥

## 🆘 Getting Help

### Self-Help Resources
1. **Notion Help Center:** notion.so/help
2. **Keyboard Shortcuts:** Ctrl/Cmd + /
3. **Template Gallery:** notion.so/templates
4. **Community Forum:** notion.so/community

### Internal Support
- **Knowledge Vault Champions:** [List department champions]
- **IT Support:** [Contact information]
- **Training Resources:** [Link to training materials]
- **Feedback Channel:** [Slack channel or email]

### Quick Support Contacts
- **Workspace Admin:** [Name and contact]
- **Technical Issues:** [IT support contact]
- **Training Questions:** [Training coordinator]
- **Process Questions:** [Process owner contact]

---

## 💡 Pro Tips

### Productivity Hacks
- **Use templates** for consistent formatting
- **Create personal dashboard** with your most-used pages
- **Set up notifications** for important updates
- **Use relations** to connect related information
- **Regular cleanup** of completed tasks and old content

### Collaboration Best Practices
- **Be specific** in comments and feedback
- **Use @ mentions** to ensure people see important messages
- **Keep titles descriptive** for better searchability
- **Update status regularly** to keep team informed
- **Link related content** to provide context

### Time-Saving Shortcuts
- **Duplicate pages** instead of starting from scratch
- **Use filters and views** to focus on relevant content
- **Bookmark frequently used pages**
- **Set up recurring templates** for regular activities
- **Use keyboard shortcuts** for common actions

---

**Print this card and keep it handy for quick reference while learning the system!**

*Last updated: [Date] | Version 1.0*
